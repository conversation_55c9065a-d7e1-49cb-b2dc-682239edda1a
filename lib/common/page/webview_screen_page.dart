/// <AUTHOR>
/// @project FlutterKit
/// @date 8/6/23

// import 'package:flutter/foundation.dart';
// import 'package:flutter/gestures.dart';
import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/Utils/web_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:encrypt/encrypt.dart' as c;

class WebviewScreenPage extends StatefulWidget {
  final String? title;
  final String url;
  final bool? needLandscape;
  final bool? needNav;
  final bool? needBackBtn; //横屏时，控制返回按钮
  final LinearGradient? gradient; //首页同行朋友圈使用
  final bool? enableLongPress; //是否启用长按事件
  final bool keepAlive;
  const WebviewScreenPage({
    super.key,
    required this.url,
    this.title,
    this.needLandscape = false,
    this.needNav = true,
    this.gradient,
    this.needBackBtn = false,
    this.enableLongPress,
    required this.keepAlive,
  });

  @override
  State<WebviewScreenPage> createState() => WebviewScreenPageState();
}

class WebviewScreenPageState extends State<WebviewScreenPage>
    with AutomaticKeepAliveClientMixin {
  late final WebViewController controller;
  double progress = 0;

  String get name => GlobalPreferences().userInfo?.user.name ?? '';
  String get mobile => GlobalPreferences().userInfo?.user.mobile ?? '';
  String get id_no => GlobalPreferences().userInfo?.user.id_no ?? '';
  String get departmentType =>
      GlobalPreferences().userInfo?.departmentTypeMin ?? '';
  String get departmentName =>
      GlobalPreferences().userInfo?.departmentNameMin ?? '';
  String get avatar => GlobalPreferences().userInfo?.user.avatar ?? '';
  String get token => GlobalPreferences().tokenValue ?? '';
  String get guid => GlobalPreferences().userInfo?.user.guid ?? '';

  bool get needProgress =>
      widget.url != AppInfo().growthChartUrl &&
      widget.url != AppInfo().friendCircleUrl; //增长榜,同行朋友圈不显示进度条

  bool loadSuccess = false;
  bool loadHasError = false;

  bool canGoBack = false;

  @override
  bool get wantKeepAlive => widget.keepAlive; // true-保持状态 false-不保持

  @override
  void initState() {
    super.initState();
    if (widget.needLandscape ?? false) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    _initializeWebView();

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
  }

  @override
  void didUpdateWidget(covariant WebviewScreenPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.url != widget.url || !loadSuccess) {
      controller.loadRequest(Uri.parse(widget.url));
    }
  }

  @override
  void dispose() {
    // 重置全局状态栏颜色为默认值
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ));

    if (widget.needLandscape ?? false) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitDown,
        DeviceOrientation.portraitUp,
      ]);
    }
    super.dispose();
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setUserAgent('XtjrFlutterAppWebview')
      ..setBackgroundColor(
          widget.gradient != null ? Colors.transparent : Colors.white)
      ..setJavaScriptMode(JavaScriptMode.unrestricted) //允许javascript
      ..addJavaScriptChannel('xtjrChannel',
          onMessageReceived: _jsChannelHandler)
      ..setOnConsoleMessage((mess) {
        debugPrint('---- web打印 ${mess.message}');
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            debugPrint('--------  web onPageStarted $url');
            ValidatorUtils.setStatusBarColor(
                Colors.transparent, Brightness.dark);
            loadSuccess = false;
            loadHasError = false;
          },
          onPageFinished: (url) {
            debugPrint('--------  web onPageFinished $url');
            if (loadHasError) {
              loadSuccess = false;
            } else {
              loadSuccess = true;
            }
            final dataMap = {
              "name": name,
              "mobile": mobile,
              "id_no": id_no,
              "department_type": departmentType,
              "department_name": departmentName,
              "avatar": avatar,
              "token": token,
              "guid": guid,
            };
            /* final secretKey = AppInfo().secretKey;
            final jsonData = jsonEncode(dataMap);
            final base64Data = base64Encode(utf8.encode(jsonData));
            final signature = generateSignature(base64Data, secretKey); */
            final jsonData = jsonEncode(dataMap);
            final encryptedData = encryptAES(jsonData);
            final signature =
                generateSignature(encryptedData, AppInfo().aesKey);
            controller.runJavaScript('''
    window.raffleForm = {
      data: '$encryptedData',
      sign: '$signature'
    };
    window.raffleFormInjected();
    console.log('onPageFinished window.raffleForm', window.raffleForm);
  ''');
            /* controller.runJavaScript('''window.raffleForm = {
  name: '$name',
  mobile: '$mobile',
  id_no: '$id_no',
  department_type: '$departmentType',
  department_name: '$departmentName',
  avatar: '$avatar',
  token: '$token',
  guid: '$guid',
};
window.raffleFormInjected()
console.log('onPageFinished window.raffleForm', window.raffleForm);
'''); */
          },
          onProgress: (p) {
            setState(() {
              progress = p / 100;
            });
          },
          onWebResourceError: (error) {
            debugPrint('--------  web onWebResourceError $error');
            loadHasError = true;
          },
          onUrlChange: (change) async {
            debugPrint('--------  web onUrlChange ${change.url}');
            canGoBack = await controller.canGoBack();
            debugPrint('--------  web canGoBack $canGoBack');
            setState(() {});
          },
          onHttpAuthRequest: (request) {
            debugPrint('--------  web onHttpAuthRequest ${request.host}');
          },
          onHttpError: (error) {
            debugPrint('--------  web onHttpError $error');
            loadHasError = true;
          },
          onNavigationRequest: (request) {
            debugPrint('--------  web onNavigationRequest ${request.url}');
            if (request.url.startsWith('https://nn.xtjzx.cn/scheme.html')) {
              _launchWeChatMiniProgram(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
        canPop: !canGoBack,
        onPopInvokedWithResult: canGoBack
            ? (didPop, result) {
                if (!didPop) {
                  controller.canGoBack().then((v) {
                    if (v) {
                      controller.goBack();
                    } else {
                      if (needProgress) NavigatorUtils.pop(context);
                    }
                  });
                }
              }
            : null,
        child: widget.needNav ?? true
            ? Scaffold(
                appBar: CommonNav(
                  title: widget.title ?? "",
                  onBack: () {
                    controller.canGoBack().then((v) {
                      if (v) {
                        controller.goBack();
                      } else {
                        NavigatorUtils.pop(context);
                      }
                    });
                  },
                ),
                body: _webViewBuilder(),
              )
            : Material(
                color: Colors.white,
                child: Stack(
                  children: [
                    Container(
                      height: 130.h,
                      decoration: BoxDecoration(gradient: widget.gradient),
                    ),
                    _webViewBuilder(),
                    if (widget.needBackBtn ?? false)
                      Positioned(
                          top: 16.h,
                          // left: 16.w,
                          child: IconButton(
                            onPressed: () async {
                              if (widget.needLandscape ?? false) {
                                await SystemChrome.setPreferredOrientations([
                                  DeviceOrientation.portraitDown,
                                  DeviceOrientation.portraitUp,
                                ]);
                              }
                              NavigatorUtils.pop(context);
                            },
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: Color(0xFF000000),
                            ),
                            iconSize: 18,
                          )),
                  ],
                ),
              ));
  }

  Widget _webViewBuilder() {
    return Column(
      children: [
        progress < 1.0 && needProgress
            ? LinearProgressIndicator(
                value: progress,
                color: AppTheme.colorBlue,
              )
            : Container(),
        Expanded(
            child: GestureDetector(
          onLongPress: !(widget.enableLongPress ?? true)
              ? null
              : () async {
                  final result = await controller.runJavaScriptReturningResult(
                      "document.getElementsByTagName('img')[0].src");
                  try {
                    final String? imageUrl =
                        jsonDecode(result as String) as String?;
                    if (imageUrl != null && imageUrl.isNotEmpty) {
                      WebUtils.saveImage(imageUrl);
                    }
                  } catch (e) {
                    final String? imageUrl = result.toString();
                    if (imageUrl != null && imageUrl.isNotEmpty) {
                      WebUtils.saveImage(imageUrl);
                    }
                  }
                },
          child: widget.enableLongPress ?? true
              ? WebViewWidget(controller: controller)
              : WebViewWidget(
                  gestureRecognizers: {}..add(
                      Factory<LongPressGestureRecognizer>(
                          () => LongPressGestureRecognizer())),
                  controller: controller,
                ),
        ))
      ],
    );
  }

  /// 公开的reload方法，供外部调用
  void reloadWebView() {
    controller.reload();
    debugPrint('WebView已刷新: ${widget.url}');
  }

  /// ---------------------------- jschannel
  void _jsChannelHandler(JavaScriptMessage message) {
    WebUtils.jsChannelHandler(message, controller, context, () {
      controller.reload();
    });
  }

  /// ---------------------------- 微信小程序
  void _launchWeChatMiniProgram(String url) {
    showDialog(
        context: context,
        builder: (_) {
          return CustomDialog(
            title: '提示',
            content: '进入微信小程序打开此页面',
            confirmButtonText: '打开',
            onCancel: () {
              NavigatorUtils.pop(_);
            },
            onConfirm: () {
              NavigatorUtils.pop(_);
              String id = url.split('id=').last;
              WeChatService().launchWeChatMiniProgram(
                  userName: 'gh_823762b08f2e',
                  path: 'pages/sharePage/sharePage?id=$id');
            },
          );
        });
  }

  /// 加密
  /* String generateSignature(String data, String secretKey) {
    final key = utf8.encode(secretKey);
    final bytes = utf8.encode(data);
    final hmacSha256 = Hmac(sha256, key);
    return hmacSha256.convert(bytes).toString();
  } */

  /// 使用 AES-CBC 模式加密数据，返回 base64 编码结果
  String encryptAES(String plainText) {
    // 创建加密所需的密钥和偏移量（IV）
    final key = c.Key.fromUtf8(AppInfo().aesKey);
    final iv = c.IV.fromUtf8(AppInfo().aesIv);

    // 初始化加密器，指定 AES + CBC 模式
    final encrypter = c.Encrypter(c.AES(key, mode: c.AESMode.cbc));

    // 加密明文，得到加密后的数据
    final encrypted = encrypter.encrypt(plainText, iv: iv);

    // 返回 Base64 编码，便于在 JS 中传输和解析
    return base64.encode(encrypted.bytes);
  }

  /// 使用 HMAC-SHA256 对加密内容进行签名
  String generateSignature(String data, String secret) {
    final hmacSha256 = Hmac(sha256, utf8.encode(secret)); // 传入密钥
    final digest = hmacSha256.convert(utf8.encode(data)); // 对数据进行签名
    return digest.toString(); // 返回十六进制签名字符串
  }
}
