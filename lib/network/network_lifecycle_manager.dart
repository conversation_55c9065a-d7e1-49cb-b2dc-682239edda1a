import 'dart:developer';
import 'package:flutter/widgets.dart';
import 'package:npemployee/network/api.dart';

/// 网络生命周期管理器
/// 负责监听应用生命周期变化并管理网络连接状态
class NetworkLifecycleManager with WidgetsBindingObserver {
  static final NetworkLifecycleManager _instance = NetworkLifecycleManager._internal();
  
  factory NetworkLifecycleManager() => _instance;
  
  NetworkLifecycleManager._internal();
  
  static NetworkLifecycleManager get instance => _instance;
  
  bool _isInitialized = false;
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  DateTime? _backgroundTime;
  
  /// 初始化网络生命周期管理器
  void initialize() {
    if (_isInitialized) return;
    
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;
    log("[NetworkLifecycleManager] Initialized");
  }
  
  /// 销毁网络生命周期管理器
  void dispose() {
    if (!_isInitialized) return;
    
    WidgetsBinding.instance.removeObserver(this);
    _isInitialized = false;
    log("[NetworkLifecycleManager] Disposed");
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    final previousState = _currentState;
    _currentState = state;
    
    log("[NetworkLifecycleManager] App lifecycle changed: $previousState -> $state");
    
    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed(previousState);
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }
  
  /// 处理应用恢复到前台
  void _handleAppResumed(AppLifecycleState previousState) {
    log("[NetworkLifecycleManager] App resumed from $previousState");
    
    // 如果从后台或非活动状态恢复，检查是否需要重置网络连接
    if (previousState == AppLifecycleState.paused || 
        previousState == AppLifecycleState.inactive ||
        previousState == AppLifecycleState.hidden) {
      
      // 计算后台时间
      if (_backgroundTime != null) {
        final backgroundDuration = DateTime.now().difference(_backgroundTime!);
        log("[NetworkLifecycleManager] App was in background for ${backgroundDuration.inSeconds} seconds");
        
        // 如果后台时间超过30秒，重置网络连接池
        if (backgroundDuration.inSeconds > 30) {
          _resetNetworkConnections();
        }
      } else {
        // 如果没有记录后台时间，也重置连接（安全起见）
        _resetNetworkConnections();
      }
    }
    
    _backgroundTime = null;
  }
  
  /// 处理应用进入后台
  void _handleAppPaused() {
    log("[NetworkLifecycleManager] App paused");
    _backgroundTime = DateTime.now();
  }
  
  /// 处理应用变为非活动状态
  void _handleAppInactive() {
    log("[NetworkLifecycleManager] App inactive");
    _backgroundTime ??= DateTime.now();
  }
  
  /// 处理应用隐藏
  void _handleAppHidden() {
    log("[NetworkLifecycleManager] App hidden");
    _backgroundTime ??= DateTime.now();
  }
  
  /// 处理应用分离
  void _handleAppDetached() {
    log("[NetworkLifecycleManager] App detached");
  }
  
  /// 重置网络连接
  void _resetNetworkConnections() {
    try {
      log("[NetworkLifecycleManager] Resetting network connections...");
      
      // 重置HTTP管理器的连接池
      httpManager.resetConnectionPool();
      
      log("[NetworkLifecycleManager] Network connections reset successfully");
    } catch (e) {
      log("[NetworkLifecycleManager] Error resetting network connections: $e");
    }
  }
  
  /// 手动重置网络连接（供外部调用）
  void forceResetNetworkConnections() {
    log("[NetworkLifecycleManager] Force resetting network connections");
    _resetNetworkConnections();
  }
  
  /// 获取当前应用生命周期状态
  AppLifecycleState get currentState => _currentState;
  
  /// 检查应用是否在前台
  bool get isAppInForeground => _currentState == AppLifecycleState.resumed;
  
  /// 检查应用是否在后台
  bool get isAppInBackground => _currentState == AppLifecycleState.paused ||
                                _currentState == AppLifecycleState.hidden;
}
